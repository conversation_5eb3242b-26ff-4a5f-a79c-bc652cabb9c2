cmake_minimum_required(VERSION 3.2)
project(lendy)
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-narrowing" CACHE STRING "Compiler flags" FORCE)
# math(EXPR stack_size "100*1024*1024")  # 计算100MB的字节数
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g -O0")

# set(CMAKE_EXE_LINKER_FLAGS "-Wl,--stack,${stack_size}") 
option(WITH_COREDEBUG   "Include additional debug-code in core"                       0)

set(CMAKE_MODULE_PATH ${CMAKE_MODULE_PATH} ${CMAKE_SOURCE_DIR}/cmake)


list(APPEND CMAKE_MODULE_PATH
  "${CMAKE_SOURCE_DIR}/cmake/macros")

if (WIN32)
	set(CMAKE_BUILD_TYPE "RelWithDebInfo")
else()
	if (WITH_COREDEBUG)
		if( NOT CMAKE_BUILD_TYPE )
			set(CMAKE_BUILD_TYPE "Debug")
			set(CMAKE_CXX_FLAGS_DEBUG "-std=c++14 -g3 -gdwarf-2 -fPIC")
		endif()
	else()
		set(CMAKE_CXX_FLAGS "-std=c++14 -O3 -fPIC")
		set(CMAKE_BUILD_TYPE "RelWithDebInfo")
	endif()
endif()

find_package(MySQL)

include(ConfigureBaseTargets)
include(ConfigureScripts)

include(Utils)
include(AutoCollect)
include(options)
include(showoptions)


set_property(GLOBAL PROPERTY USE_FOLDERS On)
set(CMAKE_VERBOSE_MAKEFILE ON)

add_definitions(-DPROTOCOL_STREAM)
# if(EXISTS "/usr/local/openssl-1.0.2/include/openssl/ssl.h")
#     set(OPENSSL_INCLUDE_DIR "/usr/local/openssl-1.0.2/include")
# else()
#     message(FATAL_ERROR "OpenSSL headers not found")
# endif()
# set(OPENSSL_ROOT_DIR "/usr")
# set(OPENSSL_INCLUDE_DIR "/usr/include")
set(OPENSSL_ROOT_DIR "/usr/local/openssl-1.0")
set(OPENSSL_USE_STATIC_LIBS TRUE)  # 如需静态链�?
# set(OPENSSL_LIBRARIES 
#     "${OPENSSL_ROOT_DIR}/lib/libssl.so" 
#     "${OPENSSL_ROOT_DIR}/lib/libcrypto.so"
# )

find_package(OpenSSL REQUIRED)


# 强制指定1.0.2版本�?�?
# set(OPENSSL_ROOT_DIR "/usr/local/openssl-1.0.2")
# set(OPENSSL_INCLUDE_DIR "${OPENSSL_ROOT_DIR}/include")

# 禁用系统默�?�搜�?
# set(OPENSSL_USE_STATIC_LIBS FALSE)
# find_package(OpenSSL 1.0.2 EXACT REQUIRED)

# set(OPENSSL_ROOT_DIR "/usr/local/openssl-1.0.2")
# set(OPENSSL_INCLUDE_DIR "${OPENSSL_ROOT_DIR}/include")
# set(OPENSSL_LIBRARIES 
#     "${OPENSSL_ROOT_DIR}/lib/libssl.a" 
#     "${OPENSSL_ROOT_DIR}/lib/libcrypto.a"
# )
# find_package(OpenSSL REQUIRED)

if(OPENSSL_FOUND)
    include_directories(${OPENSSL_INCLUDE_DIR})
endif()

set(BUILDDIR ${CMAKE_BINARY_DIR})
configure_file(
    "${CMAKE_SOURCE_DIR}/vision_data.h.in.cmake"
    "${BUILDDIR}/vision_data.h"
    @ONLY
  )

add_subdirectory(apilib)
add_subdirectory(src)

if(BUILD_EXAMPLE)
add_subdirectory(tests)
endif()