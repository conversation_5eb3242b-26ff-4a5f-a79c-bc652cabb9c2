﻿#ifndef _CALARMLOGHANDLERPRIVATEH
#define _CALARMLOGHANDLERPRIVATEH

#include "httplib.h"

class CAlarmLogHandlerPrivate
{
public:
    //告警记录模块
    static void queryNew(const httplib::Request &req, httplib::Response &res);//查询最新的报警记录
    static void query(const httplib::Request &req, httplib::Response &res);//查询
    static void remove(const httplib::Request &req, httplib::Response &res);//删除
    static void solve(const httplib::Request &req, httplib::Response &res);//处理
    static void crossline(const httplib::Request& req, httplib::Response& res);

    //告警统计模块
    //1.统计总数  统计今日数
    static void total(const httplib::Request &req, httplib::Response &res);

    //2.按天分组统计
    static void groupByDay(const httplib::Request &req, httplib::Response &res);

    //3.按视频源分组统计
    static void groupBySource(const httplib::Request &req, httplib::Response &res);

    //4.按算法分组统计
    static void groupByAlgorithm(const httplib::Request &req, httplib::Response &res);

    //告警记录批量删除
    static void batchDelete(const httplib::Request &req, httplib::Response &res);
    //车辆统计
    static void cars(const httplib::Request &req, httplib::Response &res);
};

#endif /* _CALARMLOGHANDLERPRIVATEH */


