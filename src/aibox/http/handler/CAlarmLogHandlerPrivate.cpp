﻿#include "CAlarmLogHandlerPrivate.h"
#include "../../util/StringFormat.h"
#include "../../util/Timer.h"

// #include <QtSql>
// #include <COptional.h>
// #include <QDateTime>

// #include "CDBHelper.h"
#include "../../db/DBExports.h"
#include "CHttpTools.h"
#include "CParam.h"

// #include "LogMacro.h"
// #include "json.hpp"
#include "../../log/Log.h"

#define _Alarm_Log_Table "alarm_log_table"
using namespace LogComm;

void CAlarmLogHandlerPrivate::queryNew(const httplib::Request &req, httplib::Response &res) {
    std::cout << ("queryNew start") << std::endl;
    if (!CHttpTools::checkSession(req, res)) {
        std::cout << "http.Requestsfasfafadfasd " << std::endl;
        // LOG_ERROR("http.Request", "queryNew checkSession failed");
        return;
    }
    std::cout << ("queryNew s77777777777777") << std::endl;

    int32_t count = 5; // 一共多少条
    if (req.has_param("count")) {
        count = std::stoi(req.get_param_value("count"));
    }
    //
    std::cout << "queryNew 444444 " << std::endl;
    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM);
    stmt->SetInt32(0, count);
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    LOG_INFO("http.Request", "queryNew 555555");
    // if (!query) {
    //     res.set_content(CHttpTools::json(false, u8"查询失败"), HttpContentType::json);
    //     return;
    // }
    LOG_INFO("http.Request", "queryNew 1111111");
    Json::Value jsonList;
    if (query) {
        do {
            DB::Field *field = query->Fetch();
            Json::Value json;
            json["id"] = field[0].GetInt32();
            json["name"] = field[1].GetString();
            json["ip"] = field[2].GetString();
            json["date"] = field[3].GetInt32();
            json["time"] = field[4].GetInt32();
            json["type"] = field[5].GetInt32();
            json["level"] = field[6].GetInt32();
            json["is_do"] = field[7].GetBool();
            json["image"] = field[8].GetString();
            json["video"] = field[9].GetString();
            jsonList.append(json);
        } while (query && query->NextRow());
    }

    LOG_INFO("http.Request", "queryNew 22222");
    std::string ret = "[]";
    if (!jsonList.empty()) {
        std::cout << "queryNew jsonList.empty() :" << jsonList.empty() << std::endl;
        ret = jsonList.toStyledString();
    }

    std::cout << "2222222222222queryNew ret :" << CHttpTools::jsonObj(true, ret) << std::endl;
    res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
    LOG_INFO("http.Request", "queryNew end ret:%s", ret);
}

void CAlarmLogHandlerPrivate::query(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;
    std::cout << "CAlarmLogHandlerPrivate::query start11111" << std::endl;
    Req_Read_File(page);      // 第几页(如果从第一页开始算，需要-1)
    Req_Read_File(pageCount); // 一页多少条

    if (!page.valid() || !pageCount.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }
    std::cout << "CAlarmLogHandlerPrivate::query start2222" << std::endl;
    Req_Read_File(name);
    Req_Read_File(type);
    Req_Read_File(level);
    Req_Read_File(is_do);
    Req_Read_File(date);
    Req_Read_File(ip);

    const int32_t vpage = std::stoi(page.value());
    const int32_t vpageCount = std::stoi(pageCount.value());

    std::string condition;
    std::cout << "CAlarmLogHandlerPrivate::query start333" << std::endl;
    if (name.valid() && !name.value().empty()) { // 数据源
        condition += fmt::format(" and b.name like '%{}%' ", name.value());
    }

    if (type.valid() && !type.value().empty()) { // 告警类型

        int _type = std::stoi(type.value());
        condition += fmt::format(" and a.type={} ", _type);
    }

    if (level.valid() && !level.value().empty()) { // 危险等级
        int8_t _level = std::stoi(level.value());
        condition += fmt::format(" and a.level={} ", _level);
    }

    if (is_do.valid() && !is_do.value().empty()) { // 处理状态
        int _is_do = std::stoi(is_do.value());
        condition += fmt::format(" and a.is_do={} ", _is_do);
    }

    if (date.valid() && !date.value().empty()) { // 告警时间

        LOG_INFO("server.http", " alarm_log date: [%s] ", date.value().c_str());
        auto stamp = Util::convertToTimestamp(date.value());
        condition += fmt::format(" and a.date > {} ", stamp);
    }
    if (ip.valid() && !ip.value().empty()) { // 告警时间

        condition += fmt::format(" and b.ip = '{}' ", ip.value());
    }

    // DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_COUNT);
    // DB::PreparedQueryResult result = DB::LogonDatabasePool.Query(stmt);
    std::string sql1 = fmt::format("select  count(a.id)"
                                   " from alarm_log_table a "
                                   " join input_source_table b on a.camera_id=b.id "
                                   " where a.enable=1 {}",
                                   condition);
    DB::QueryResult result = DB::LogonDatabasePool.Query(sql1.c_str());
    int totalCount = 0;
    int totalPage = 0;
    if (result) {
        DB::Field *field = result->Fetch();
        totalCount = field[0].GetInt32();
        totalPage = std::ceil(totalCount / vpageCount);
        int offset = 0;
        if (vpageCount * (vpage - 1) >= 0) {
            offset = vpageCount * (vpage - 1);
        }
        std::string strLimit = fmt::format(" order by a.id desc LIMIT {0} OFFSET {1} ;", vpageCount, offset);

        std::string sql = fmt::format("select a.id,b.name,b.ip,a.date,a.time,a.type,a.level,a.is_do,a.image,a.video "
                                      " from alarm_log_table a "
                                      " join input_source_table b on a.camera_id=b.id "
                                      " where a.enable=1 {} {}",
                                      condition, strLimit);

        LOG_INFO("server.http", "alarm_log query condition: [%s] strLimit:[%s]", condition.c_str(), strLimit.c_str());
        DB::QueryResult query = DB::LogonDatabasePool.Query(sql.c_str());
        std::cout << "query sql: " << sql << std::endl;
        if (query) {
            Json::Value json;
            do {
                Json::Value value;
                auto field = query->Fetch();
                value["id"] = field[0].GetInt32();
                value["name"] = field[1].GetString();
                value["ip"] = field[2].GetString();
                value["date"] = field[3].GetInt32();
                value["time"] = field[4].GetInt32();
                value["type"] = field[5].GetInt32();
                value["level"] = field[6].GetInt32();
                value["is_do"] = field[7].GetInt8();
                value["image"] = field[8].GetString();
                value["video"] = field[9].GetString();

                json.append(value);
            } while (query->NextRow());

            Json::Value jsonObj;
            jsonObj["page"] = vpage;
            jsonObj["totalPage"] = totalPage;
            jsonObj["count"] = totalCount;
            jsonObj["list"] = json;
            std::string ret = jsonObj.toStyledString();

            res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
            res.status = httplib::StatusCode::OK_200;
            return;
        }
        res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    }
}


void CAlarmLogHandlerPrivate::cars(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

   
    Req_Read_File(page);      // 第几页(如果从第一页开始算，需要-1)
    Req_Read_File(pageCount); // 一页多少条

    if (!page.valid() || !pageCount.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }
    Req_Read_File(name);
    Req_Read_File(type);
    Req_Read_File(level);
    Req_Read_File(is_do);
    Req_Read_File(date);
    Req_Read_File(ip);

    const int32_t vpage = std::stoi(page.value());
    const int32_t vpageCount = std::stoi(pageCount.value());

    std::string condition;
    std::cout << "CAlarmLogHandlerPrivate::query start333" << std::endl;
    if (name.valid() && !name.value().empty()) { // 数据源
        condition += fmt::format(" and b.name like '%{}%' ", name.value());
    }

    if (type.valid() && !type.value().empty()) { // 告警类型
        int _type = std::stoi(type.value());
        condition += fmt::format(" and a.type={} ", _type);
    }else{
        int _type1 = 4006;
        int _type2 = 3007;
        condition += fmt::format(" and ( a.type = {} or a.type = {} )", _type1, _type2);
    }

    if (level.valid() && !level.value().empty()) { // 危险等级
        int8_t _level = std::stoi(level.value());
        condition += fmt::format(" and a.level={} ", _level);
    }

    if (is_do.valid() && !is_do.value().empty()) { // 处理状态
        int _is_do = std::stoi(is_do.value());
        condition += fmt::format(" and a.is_do={} ", _is_do);
    }

    if (date.valid() && !date.value().empty()) { // 告警时间

        LOG_INFO("server.http", " alarm_log date: [%s] ", date.value().c_str());
        auto stamp = Util::convertToTimestamp(date.value());
        condition += fmt::format(" and a.date > {} ", stamp);
    }
    if (ip.valid() && !ip.value().empty()) { // 告警时间

        condition += fmt::format(" and b.ip = '{}' ", ip.value());
    }

    // DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_COUNT);
    // DB::PreparedQueryResult result = DB::LogonDatabasePool.Query(stmt);
    std::string sql1 = fmt::format("select  count(a.id)"
                                   " from alarm_log_table a "
                                   " join input_source_table b on a.camera_id=b.id "
                                   " where a.enable=1 {}",
                                   condition);
    DB::QueryResult result = DB::LogonDatabasePool.Query(sql1.c_str());
    int totalCount = 0;
    int totalPage = 0;
    if (result) {
        DB::Field *field = result->Fetch();
        totalCount = field[0].GetInt32();
        totalPage = std::ceil(totalCount / vpageCount);
        int offset = 0;
        if (vpageCount * (vpage - 1) >= 0) {
            offset = vpageCount * (vpage - 1);
        }
        std::string strLimit = fmt::format(" order by a.id desc  LIMIT {0} OFFSET {1} ;", vpageCount, offset);

        std::string sql = fmt::format("select a.id,b.name,b.ip,a.date,a.time,a.type,a.level,a.is_do,a.image,a.video ,a.license_plate"
                                      " from alarm_log_table a "
                                      " join input_source_table b on a.camera_id=b.id "
                                      " where a.enable=1 {} {}",
                                      condition, strLimit);

        LOG_INFO("server.http", "alarm_log query condition: [%s] strLimit:[%s]", condition.c_str(), strLimit.c_str());
        DB::QueryResult query = DB::LogonDatabasePool.Query(sql.c_str());
        std::cout << "query sql: " << sql << std::endl;
        if (query) {
            Json::Value json;
            do {
                Json::Value value;
                auto field = query->Fetch();
                value["id"] = field[0].GetInt32();
                value["name"] = field[1].GetString();
                value["ip"] = field[2].GetString();
                value["date"] = field[3].GetInt32();
                value["time"] = field[4].GetInt32();
                value["type"] = field[5].GetInt32();
                value["level"] = field[6].GetInt32();
                value["is_do"] = field[7].GetInt8();
                value["image"] = field[8].GetString();
                value["video"] = field[9].GetString();
                value["license_plate"] = field[10].GetString();

                json.append(value);
            } while (query->NextRow());

            Json::Value jsonObj;
            jsonObj["page"] = vpage;
            jsonObj["totalPage"] = totalPage;
            jsonObj["count"] = totalCount;
            jsonObj["list"] = json;
            std::string ret = jsonObj.toStyledString();

            res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
            res.status = httplib::StatusCode::OK_200;
            return;
        }
        res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    }
}



void CAlarmLogHandlerPrivate::crossline(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(page);      // 第几页(如果从第一页开始算，需要-1)
    Req_Read_File(pageCount); // 一页多少条

    if (!page.valid() || !pageCount.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }

    Req_Read_File(name);
    Req_Read_File(type);
    Req_Read_File(level);
    Req_Read_File(is_do);
    Req_Read_File(date);

    const int32_t vpage = std::stoi(page.value());
    const int32_t vpageCount = std::stoi(pageCount.value());

    std::string condition;

    if (name.valid() && !name.value().empty()) { // 数据源
        condition += fmt::format(" and b.name like '%{}%' ", name.value());
    }

    if (type.valid() && !type.value().empty()) { // 告警类型
        int _type = std::stoi(type.value());
        condition += fmt::format(" and a.type={} ", _type);
    }

    if (level.valid() && !level.value().empty()) { // 危险等级
        int8_t _level = std::stoi(level.value());
        condition += fmt::format(" and a.level={} ", _level);
    }

    if (is_do.valid() && !is_do.value().empty()) { // 处理状态
        int _is_do = std::stoi(is_do.value());

        condition += fmt::format(" and a.is_do={} ", _is_do);
    }

    if (date.valid() && !date.value().empty()) { // 告警时间
        int64_t beginTime = std::stoll(date.value());
        condition += fmt::format(" and a.time={} ", beginTime);
    }

    if (1) {

        std::string sql = fmt::format("select count(a.id) "
                                      "from alarm_log_table a "
                                      "join input_source_table b on a.camera_id=b.id "
                                      "where a.enable=1 {} ",
                                      condition);
        DB::QueryResult query = DB::LogonDatabasePool.Query(sql.c_str());

        int totalCount = 0;
        if (query) {
            DB::Field *field = query->Fetch();
            totalCount = field[0].GetInt32();
        }
        const int totalPage = std::ceil(totalCount * 1.0 / vpageCount);
        if (totalCount < 1) {
            res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
            return;
        }
        int offset = vpageCount * (vpage - 1) < 0;
        if (offset < 0) {
            offset = 0;
        }
        std::string strLimit = fmt::format(" order by a.date desc, a.time desc LIMIT {0} OFFSET {1?}", vpageCount, offset);

        std::string sql1 = fmt::format("select a.id,b.name,b.ip,a.date,a.time,a.type,a.level,a.is_do,a.image,a.video "
                                       "from alarm_log_table a "
                                       "join input_source_table b on a.camera_id=b.id "
                                       "where a.enable=1 {} {}",
                                       condition, strLimit);

        DB::QueryResult query1 = DB::LogonDatabasePool.Query(sql1.c_str());
        // 查询总记录

        if (!query1) {
            res.set_content(CHttpTools::json(false, u8"查询失败"), HttpContentType::json);

        } else {
            Json::Value json;
            do {
                Json::Value value;
                auto field = query1->Fetch();
                value["id"] = field[0].GetInt32();
                value["deviceId"] = field[1].GetString();
                value["name"] = field[2].GetString();
                value["ip"] = field[3].GetInt32();
                value["dtime"] = field[4].GetInt32();
                value["type"] = field[5].GetInt32();
                value["direction"] = field[6].GetInt32();
                value["count"] = field[7].GetBool();
                value["image"] = field[8].GetString();
                value["outCount"] = 0;
                value["totalCount"] = 0;

                json.append(value);
            } while (query1->NextRow());

            Json::Value jsonObj;
            jsonObj["page"] = vpage;
            jsonObj["totalPage"] = totalPage;
            jsonObj["count"] = totalCount;
            jsonObj["list"] = json;
            std::string ret = jsonObj.toStyledString();

            res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
        }
        //

        res.status = httplib::StatusCode::OK_200;
    }
}

void CAlarmLogHandlerPrivate::remove(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    COptional<int32_t> id; // 告警类型
    if (req.has_file("id")) {
        id = std::stoi(req.get_file_value("id").content);
    }

    if (!id.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少id参数"), HttpContentType::json);
        return;
    }

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_REMOVE);
    stmt->SetInt32(0, id.value());
    DB::LogonDatabasePool.Query(stmt);
    
    res.set_content(CHttpTools::json(true, u8"删除成功"), HttpContentType::json);
    

    res.status = httplib::StatusCode::OK_200;
}

void CAlarmLogHandlerPrivate::solve(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    COptional<int32_t> id; // 告警类型
    if (req.has_file("id")) {
        id = std::stoi(req.get_file_value("id").content);
    }

    if (!id.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少id参数"), HttpContentType::json);
        return;
    }

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_SOLVE);
    stmt->SetInt32(0, id.value());
    DB::LogonDatabasePool.Query(stmt);

    res.set_content(CHttpTools::json(true, u8"处理成功"), HttpContentType::json);

    res.status = httplib::StatusCode::OK_200;
}

void CAlarmLogHandlerPrivate::total(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    DB::PreparedStatement *stmt1 = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_TOTAL_COUNT);
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt1);
    int count1 = 0;
    if (query) {
        DB::Field *field = query->Fetch();
        count1 = field[0].GetInt32();
    }
    auto today = DB::getSomeDayTimeSec(0);
    std::string sql = fmt::format("select count(id) from alarm_log_table where enable=1 and date={}", today);
    DB::QueryResult query1 = DB::LogonDatabasePool.Query(sql.c_str());

    int count2 = 0;
    if (query1) {
        DB::Field *field = query1->Fetch();
        count2 = field[0].GetInt32();
    }

    Json::Value jsonObj;
    jsonObj["total"] = count1;
    jsonObj["today"] = count2;
    std::string stdStr = jsonObj.toStyledString();

    res.set_content(CHttpTools::jsonObj(true, stdStr), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}

void CAlarmLogHandlerPrivate::groupByDay(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    COptional<int> day(6); // 7天
    if (req.has_param("day")) {
        day = std::stoi(req.get_param_value("day")) - 1;
    }

    auto daySec = DB::getSomeDayTimeSec(day.value());
    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_GROUP_BY_DAY);
    stmt->SetInt64(0, daySec);
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (query) {
        Json::Value jsonList;
        do {
            DB::Field *field = query->Fetch();
            Json::Value json;
            json["date"] = field[0].GetInt32();
            json["count"] = field[1].GetInt32();
            jsonList.append(json);
        } while (query->NextRow());

        auto ret = jsonList.toStyledString();
        res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
        return;
    } else {
        res.set_content(CHttpTools::jsonObj(true, "[]"), HttpContentType::json);
        res.status = httplib::StatusCode::OK_200;
    }
}

void CAlarmLogHandlerPrivate::groupBySource(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    COptional<int> day(6); // 7天
    if (req.has_param("day")) {
        day = std::stoi(req.get_param_value("day")) - 1;
    }

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_GROUP_BY_SOURCE);
    auto daySec = DB::getSomeDayTimeSec(day.value());
    stmt->SetInt64(0, daySec);
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (!query) {
        res.set_content(CHttpTools::json(true, "[]"), HttpContentType::json);

    } else {
        Json::Value jsonList;
        do {
            DB::Field *field = query->Fetch();
            Json::Value json;
            json["name"] = field[0].GetString();
            json["count"] = field[1].GetInt32();
            jsonList.append(json);
        } while (query->NextRow());

        auto ret = jsonList.toStyledString();
        res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
    }

    res.status = httplib::StatusCode::OK_200;
}

void CAlarmLogHandlerPrivate::batchDelete(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    Req_Read_File(ids);

    if (!ids.valid()) {
        res.set_content(CHttpTools::json(false, u8"缺少参数"), HttpContentType::json);
        return;
    }
    const std::string sIds = ids.value();
    std::string err("");
    Json::Value j = Util::stringToJson(sIds, err);
    if (!err.empty()) {
        res.set_content(CHttpTools::json(false, u8"参数错误"), HttpContentType::json);
        return;
    }
    std::string s("");
    for (auto item : j) {
        s += item.asString() + ",";
    }
    s.erase(s.end() - 1);

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_BATCH_DELETE);
    stmt->SetString(0, s);
    DB::LogonDatabasePool.Query(stmt);

    res.set_content(CHttpTools::json(true, u8"删除成功"), HttpContentType::json);
    res.status = httplib::StatusCode::OK_200;
}

void CAlarmLogHandlerPrivate::groupByAlgorithm(const httplib::Request &req, httplib::Response &res) {
    if (!CHttpTools::checkSession(req, res))
        return;

    COptional<int> day(6); // 7天
    if (req.has_param("day")) {
        day = std::stoi(req.get_param_value("day")) - 1;
    }

    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_ALARM_GROUP_BY_ALGORITHM);
    auto daySec = DB::getSomeDayTimeSec(day.value());
    stmt->SetInt64(0, daySec);
    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (!query) {
        res.set_content(CHttpTools::json(true, "[]"), HttpContentType::json);

    } else {
        Json::Value jsonList;
        do {
            DB::Field *field = query->Fetch();
            Json::Value json;
            json["name"] = field[0].GetString();
            json["count"] = field[1].GetInt32();
            jsonList.append(json);
        } while (query->NextRow());

        auto ret = jsonList.toStyledString();
        res.set_content(CHttpTools::jsonObj(true, ret), HttpContentType::json);
    }

    res.status = httplib::StatusCode::OK_200;
}
