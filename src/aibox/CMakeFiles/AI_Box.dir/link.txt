/usr/bin/c++ -std=c++14 -O3 -fPIC -g -rdynamic CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o CMakeFiles/AI_Box.dir/RoomListManager.cpp.o CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o CMakeFiles/AI_Box.dir/main.cpp.o CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o CMakeFiles/AI_Box.dir/http/CHandler.cpp.o CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o -o AI_Box  -Wl,-rpath,/home/<USER>/AI_Box/apilib/db:/home/<USER>/AI_Box/apilib/net:/usr/local/lib:/home/<USER>/AI_Box/apilib/log:/home/<USER>/AI_Box/apilib/util: -lJSONCPP_LIB-NOTFOUND ../../apilib/db/DataBase.so ../../apilib/net/Net.so -ljsoncpp -lstdc++fs -lxlnt /usr/local/lib/libboost_filesystem.so.1.88.0 /usr/lib/x86_64-linux-gnu/libmysqlclient.so ../../apilib/log/Log.so ../../apilib/util/Util.so ../../apilib/dep/fmt/fmt.a -ldl -lpthread -lssl -lcrypto -luuid /usr/local/lib/libboost_atomic.so.1.88.0 /usr/local/lib/libboost_system.so.1.88.0 
