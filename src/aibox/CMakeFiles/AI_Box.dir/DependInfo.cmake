
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/AI_Box/src/aibox/AttemperEngineSink.cpp" "src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/AttemperEngineSink.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/RoomListManager.cpp" "src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/RoomListManager.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/ServiceUnits.cpp" "src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/ServiceUnits.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/data/CDirManager.cpp" "src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/data/CDirManager.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/data/CGlobal.cpp" "src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/data/CGlobal.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/data/CServerGlobal.cpp" "src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/data/CServerGlobal.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/data/CUserInfo.cpp" "src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/data/CUserInfo.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/CHandler.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/CHandler.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/HttpServer.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/HttpServer.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CAlarmLogHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlarmLogHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CAlgorithmHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CAlgorithmHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CBaseBankHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CBaseBankHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CDefaultHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CDefaultHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CFileHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CFileHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CHttpTools.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CHttpTools.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CInputSourceHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CInputSourceHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/COverlineHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/COverlineHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CSysHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CSysHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CTools.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CTools.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/http/handler/CUserHandlerPrivate.cpp" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/http/handler/CUserHandlerPrivate.cpp.o.d"
  "/home/<USER>/AI_Box/src/aibox/main.cpp" "src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o" "gcc" "src/aibox/CMakeFiles/AI_Box.dir/main.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
