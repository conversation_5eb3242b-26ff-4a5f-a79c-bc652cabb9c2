#include "AttemperEngineSink.h"
#include "CMD_Correspond.h"
#include "CMD_LogonServer.h"
#include "INIReader.h"
#include "Implementation/LogonDatabase.h"
#include "Log.h"
#include "ServiceUnits.h"
#include "StringFormat.h"
#include "StringUtility.h"
#include "algconfig.h"
#include "data/CDirManager.h"
#include "data/CGlobal.h"
#include "http/handler/CTools.h"

#include <iostream>

#include <string>

#define MAX_LINK_COUNT 512
#define OPEN_SWITCH 1
#define CLIENT_SWITCH 0

namespace AiBox {
using namespace LogComm;

#define IDI_LOAD_GAME_LIST          1           // 加载列表
#define IDI_CONNECT_CORRESPOND      2           // 重连标识
#define IDI_CONNECT_CAMERA          3           // 推流重连后发送摄像头信息

#define LOGON_FAILURE(linkid, errorcode)                                                                               \
    if (OnLogonFailure(linkid, errorcode)) {                                                                           \
        return true;                                                                                                   \
    }

CAttemperEngineSink::CAttemperEngineSink() {}

CAttemperEngineSink::~CAttemperEngineSink() {}

void CAttemperEngineSink::Release() {}

void *CAttemperEngineSink::QueryInterface(GGUID uuid) {
    QUERY_INTERFACE(IAttemperEngineSink, uuid);
    QUERY_INTERFACE_IUNKNOWNEX(IAttemperEngineSink, uuid);
    return nullptr;
}

bool CAttemperEngineSink::OnAttemperEngineStart(IUnknownEx *pIUnknownEx) {
    m_pBindParameter = new tagBindParameter[MAX_LINK_COUNT];
    return true;
}

bool CAttemperEngineSink::OnAttemperEngineConclude(IUnknownEx *pIUnknownEx) {
    m_pITCPSocketService = nullptr;
    return false;
}

bool CAttemperEngineSink::OnEventTCPSocketLink(uint16 wServiceID, int iErrorCode) {
    // 协调连接
    if (wServiceID == NETWORK_CORRESPOND) {
        // 错误判断
        if (iErrorCode != 0) {
            int iConnectTime = sConfigMgr->GetInt32("LocalNet", "ConnectTime", 5);
            LOG_INFO("server.logon", "Correspond server connection failed [ %d ], will reconnect in %d seconds",
                     iErrorCode, iConnectTime);
            m_pITimerEngine->SetTimer(IDI_CONNECT_CORRESPOND, iConnectTime * 1000, 1);
            return false;
        }

        // 提示消息
        LOG_INFO("server.logon", "Registering Loginserver...");

        // 变量定义
        CMD_CS_C_RegisterLogon RegisterLogon;
        memset(&RegisterLogon, 0, sizeof(RegisterLogon));

        // 设置变量
#if LENDY_PLATFORM == LENDY_PLATFORM_WINDOWS
        sprintf_s(RegisterLogon.szServerName, "%s", sConfigMgr->Get("LocalNet", "Name", "").c_str());
        sprintf_s(RegisterLogon.szServerAddr, "%s", sConfigMgr->Get("LocalNet", "WinBindIP", "").c_str());
#else
        snprintf(RegisterLogon.szServerName, sizeof(RegisterLogon.szServerName), "%s",
                 sConfigMgr->Get("LocalNet", "Name", "").c_str());
        snprintf(RegisterLogon.szServerAddr, sizeof(RegisterLogon.szServerAddr), "%s",
                 sConfigMgr->Get("LocalNet", "LinuxBindIP", "").c_str());
#endif
        std::cout << "SUB_CS_C_REGISTER_LOGON1111" << std::endl;
        // 发送数据
        m_pITCPSocketService->SendData(MDM_CS_REGISTER, SUB_CS_C_REGISTER_LOGON, &RegisterLogon, sizeof(RegisterLogon));

        return true;
    }

    return true;
}

bool CAttemperEngineSink::OnEventTCPSocketShut(uint16 wServiceID, uint8 cbShutReason) {
    // 协调连接
    if (wServiceID == NETWORK_CORRESPOND) {
        // 重连判断
        // if (m_bNeekCorrespond == true)
        {
            // 提示消息
            int iConnectTime = sConfigMgr->GetInt32("LocalNet", "ConnectTime", 5);
            LOG_INFO("server.logon",
                     "The connection to the correspond server is closed, and will reconnect in %d seconds",
                     iConnectTime);

            // 设置时间
            assert(m_pITimerEngine != nullptr);
            m_pITimerEngine->SetTimer(IDI_CONNECT_CORRESPOND, iConnectTime * 1000, 1);
            return true;
        }
    }
    return false;
}

bool CAttemperEngineSink::OnEventTCPSocketRead(uint16 wServiceID, TCP_Command Command, void *pData, uint16 wDataSize) {
    // 协调连接
    if (wServiceID == NETWORK_CORRESPOND) {
        switch (Command.wMainCmdID) {
        case MDM_CS_REGISTER: // 注册服务
        {
            return OnTCPSocketMainRegister(Command.wSubCmdID, pData, wDataSize);
        }
        case MDM_CS_ROOM_INFO: // 服务信息
        {
            return OnTCPSocketMainServiceInfo(Command.wSubCmdID, pData, wDataSize);
        }
        }
    }
    return false;
}

bool CAttemperEngineSink::OnEventTCPNetworkBind(uint32 dwClientAddr, uint32 dwSocketID) {
    // 获取索引
    assert(dwSocketID < MAX_LINK_COUNT);
    if (dwSocketID >= MAX_LINK_COUNT)
        return false;

    // 变量定义
    tagBindParameter *pBindParameter = (m_pBindParameter + dwSocketID);

    // 设置变量
    pBindParameter->dwSocketID = dwSocketID;
    pBindParameter->dwClientAddr = dwClientAddr;
    std::cout << "Client connected: " << dwClientAddr << ", Socket ID: " << dwSocketID << std::endl;
    m_ClientInfo.dwSocketID = dwSocketID;
    m_ClientInfo.dwClientAddr = dwClientAddr;
    this->m_pITimerEngine->SetTimer(IDI_CONNECT_CAMERA,3*1000,1);
    return true;
}

bool CAttemperEngineSink::OnEventTCPNetworkShut(uint32 dwClientAddr, uint32 dwSocketID) {
    memset((m_pBindParameter + dwSocketID), 0, sizeof(tagBindParameter));
    return true;
}

bool CAttemperEngineSink::OnEventTCPNetworkRead(Net::TCP_Command Command, void *pData, uint16 wDataSize,
                                                uint32 dwSocketID) {
    switch (Command.wMainCmdID) {
    case MDM_MB_LOGON: // 登录命令
    {
        return OnTCPNetworkMainMBLogon(Command.wSubCmdID, pData, wDataSize, dwSocketID);
    }
    case MDM_CS_SWARN: {

        return OnTCPNetworkMainCSImage(Command.wSubCmdID, pData, wDataSize, dwSocketID);
    }
    }
    return false;
}
bool CAttemperEngineSink::OnEventControl(uint16 wControlID, void *pData, uint16 wDataSize) {
    switch (wControlID) {
    case SUC_LOAD_DB_GAME_LIST: {
        // 查询类型
        tagGameKind GameKind = {};
        GameKind.wKindID = 104;
        GameKind.wGameID = 104;

        GameKind.dwOnLineCount = 0;
        GameKind.dwAndroidCount = 0;
        GameKind.dwFullCount = 100;
        m_RoomListManager.InsertGameKind(&GameKind);

        // 事件通知
        ControlResult ControlResult;
        ControlResult.cbSuccess = 1;
        SrvUnitsMgr->PostControlRequest(UDC_LOAD_DB_LIST_RESULT, &ControlResult, sizeof(ControlResult));
        return true;
    }
    case SUC_CONNECT_CORRESPOND: {
        // 发起连接
        m_pITCPSocketService->Connect(sConfigMgr->Get("CorrespondNet", "BindIP", "127.0.0.1"),
                                      sConfigMgr->GetInt32("CorrespondNet", "Port", 8600));
        return true;
    }
    case SUC_MDM_MB_CAMERA: {
        onCameraStatusChange();
        return true;
    }
    case SUC_MDM_MB_SDEEPIMGLIB:
    {
        onUpdatesDeepimglib(pData,wDataSize);
        return true;
    }
    }
    return false;
}

void CAttemperEngineSink::onUpdatesDeepimglib(void* pData,uint16 wDataSize)
{
    TCP_block block;
    block.nblockcount = 1;
    block.nblockid = 1;
    block.nblockGUID = Util::generate_compressed_uuid();
    char buff[1024 ] = {0};
    memcpy(buff, &block, sizeof(block));
    memcpy(buff + sizeof(block), pData, wDataSize);
    std::string strData = std::string(buff, sizeof(block) + wDataSize);

    LOG_INFO("server.logon", "onUpdatesDeepimglib msgsize:%d oldDatasize[%d]", strData.size(), wDataSize);
    m_pITCPNetworkEngine->SendData(m_ClientInfo.dwSocketID, MDM_CS_ALG, SUB_CS_ALG_DLIB, (void*)strData.data(),
                                   strData.size());
}

void CAttemperEngineSink::onCameraStatusChange() {

    std::cout << "---------------Camera control event received.-------------" << std::endl;
    DB::PreparedStatement *stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_PUSH_STREAM);

    DB::PreparedQueryResult query = DB::LogonDatabasePool.Query(stmt);
    if (query) {
        // 变量定义
        std::vector<std::string> bindValues;
        std::string str = "";
        CameraInfolist cameraList;
        std::vector<int> cameraIdList;
        do {
            DB::Field *field = query->Fetch();

            CameraInfo cameraInfo;
            cameraInfo.ID = field[0].GetInt32();
            cameraInfo.IP = field[1].GetString();
            cameraInfo.RTSP = field[2].GetString();
            int start = field[3].GetInt32();
            if (start >= 1) {
                // todo 判断start  1 才发送
                cameraList.CameraInfoList.push_back(cameraInfo);

                cameraIdList.push_back(cameraInfo.ID);
            }

        } while (query->NextRow());

        std::string cameraconfig = xpack::json::encode(cameraList);
        TCP_block block;
        block.nblockcount = 1;
        block.nblockid = 1;
        block.nblockGUID = Util::generate_compressed_uuid();
        char buff[1024 * 10] = {0};
        memcpy(buff, &block, sizeof(block));
        memcpy(buff + sizeof(block), cameraconfig.data(), cameraconfig.size());
        std::string strData = std::string(buff, sizeof(block) + cameraconfig.size());

        std::cout << "block.nblockGUID:" << block.nblockGUID << "Camera configuration: " << cameraconfig << std::endl;
        m_pITCPNetworkEngine->SendData(m_ClientInfo.dwSocketID, MDM_CS_CAMERA, SUB_CS_CAMERA_INFO, (void*)strData.data(),
                                       strData.size());

        // 获取摄像头配置参数
        if (cameraIdList.size() > 0) {

            // DB::PreparedStatement *stmt1 = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALG_CONF_LIST2);
            std::string strIdList = "";
            DB::vectorToString(cameraIdList, strIdList);
            // stmt1->SetString(0, strIdList);

            std::string lineSql = fmt::format(
                "SELECT id, camera_id,type,direction,start_points_x,start_points_y,end_points_x,end_points_y "
                "FROM overline_log_table WHERE camera_id IN ( {} )",
                strIdList);
            auto lineQuery = DB::LogonDatabasePool.Query(lineSql.c_str());
            std::map<int, std::map<int, std::vector<scross_line>>> lineMap;
            if (lineQuery) {
                do {
                    scross_line line;
                    auto field = lineQuery->Fetch();
                    int index = 0;
                    auto id = field[index++].GetInt32();
                    auto cameraId = field[index++].GetInt32();
                    auto type = field[index++].GetInt32();
                    auto direction = field[index++].GetInt32();
                    auto start_points_x = field[index++].GetString();
                    auto start_points_y = field[index++].GetString();
                    auto end_points_x = field[index++].GetString();
                    auto end_points_y = field[index++].GetString();
                    line.dir = (orientation)direction;
                    line.line_id = id;

                    line.line_points1.x = std::stoi(start_points_x.c_str());
                    line.line_points1.y = std::stoi(start_points_y.c_str());

                    line.line_points2.x = std::stoi(end_points_x.c_str());
                    line.line_points2.y = std::stoi(end_points_y.c_str());
                    if (lineMap.find(cameraId) == lineMap.end()) {
                        std::vector<scross_line> v;
                        v.emplace_back(line);
                        std::map<int, std::vector<scross_line>> algMap;
                        algMap[type] = v;
                    } else {
                        auto algMap = lineMap.find(cameraId);
                        auto it = algMap->second.find(type);
                        if (it != algMap->second.end()) {
                            it->second.emplace_back(line);
                        } else {
                            std::vector<scross_line> v;
                            v.emplace_back(line);
                            algMap->second.insert(std::pair<int, std::vector<scross_line>>(type, v));
                        }
                    }

                } while (lineQuery->NextRow());
            }

            std::string cameraSql = fmt::format(
                "SELECT `camera_id`,`alg_id`,`cycle`, `dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                "`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`polygons`, `width`, `height`,`wb` FROM "
                "camera_alg_conf where `camera_id` in ( {} ) ;",
                strIdList);
            sParam_idlist cameraParamList;
            auto query1 = DB::LogonDatabasePool.Query(cameraSql.c_str());
            std::string jsonErr = "";
            if (query1) {
                do {
                    auto field = query1->Fetch();
                    // camera_id`,`alg_id`,`cycle`, `dp`,`dp_id`,`interval`,`hasTimes`,`level`, "
                    //"`people_count`,`thrs`,`vehicletype`,`vehs`,`lines`,`polygons`, `width`, `height`,`wb`
                    int index = 0;
                    int camera_id = field[index++].GetInt32();
                    int alg_id = field[index++].GetInt32();
                    int cycle = field[index++].GetInt32();
                    int dp = field[index++].GetInt32();
                    int dp_id = field[index++].GetInt32();
                    int interval = field[index++].GetInt32();
                    int hasTimes = field[index++].GetInt32();
                    int level = field[index++].GetInt32();
                    int people_count = field[index++].GetInt32();
                    std::string thrs = field[index++].GetString();
                    std::string vehicletype = field[index++].GetString();
                    std::string vehs = field[index++].GetString();
                    std::string lines = field[index++].GetString();
                    std::string polygons = field[index++].GetString();
                    int width = field[index++].GetInt32();
                    int height = field[index++].GetInt32();
                    int wb = field[index++].GetInt32();
                    sparam_id paramId;
                    paramId.cameraid = camera_id;

                    sParam param;
                    try {

                        jsonErr.clear();
                        // thrs
                        Json::Value jVal = Util::stringToJson(thrs, jsonErr);
                        if (jVal.isArray()) {
                            for (auto item : jVal) {
                                param.thrs.push_back(item.asFloat());
                            }
                        }

                        // lines
                        auto algMap = lineMap.find(camera_id);
                        if (algMap != lineMap.end()) {
                            auto lineVec = algMap->second.find(alg_id);
                            if (lineVec != algMap->second.end()) {
                                param.lines.insert(param.lines.end(), lineVec->second.begin(), lineVec->second.end());
                            }
                        }

                        // polygons
                        jsonErr.clear();
                        Json::Value polygonsVal = Util::stringToJson(polygons, jsonErr);
                        if (jsonErr.empty()) {
                            for (auto jv : polygonsVal) {
                                auto polygonsValList = jv["points"];
                                std::vector<spoint> vec;
                                if (polygonsValList.isArray()) {
                                    for (auto v : polygonsVal) {

                                        spoint p;
                                        p.x = v["x"].asInt();
                                        p.y = v["y"].asInt();
                                        
                                        vec.emplace_back(p);
                                    }
                                }
                                if (vec.size() > 0) {
                                    param.mask_points.emplace_back(vec);
                                }
                            }
                        }

                        // param.mask_points
                        param.Post_person = people_count;

                        param.warn_interval = interval;
                        param.warn_thr = hasTimes;
                        param.warn_window = cycle;
                        param.warn_level = level;

                        param.algid = (algtype)alg_id;

                    } catch (const std::exception &e) {
                        std::cerr << e.what() << '\n';
                    }

                    bool find = false;
                    for (auto &paramList : cameraParamList.algparamidList) {
                        if (paramList.cameraid == camera_id) {
                            paramList.param.emplace_back(param);
                            find = true;
                        }
                    }
                    if (!find) {
                        paramId.param.emplace_back(param);
                        cameraParamList.algparamidList.emplace_back(paramId);
                    }

                } while (query1->NextRow());
            }

            std::string cameraconfigParam = xpack::json::encode(cameraParamList);
            int sendmax = 1024 * 10 - sizeof(TCP_block);
            int eleSize = cameraconfigParam.size();
            TCP_block block;
            block.nblockGUID = 999; // Util::generate_compressed_uuid();
            if (cameraconfigParam.size() > sendmax) {
                block.nblockcount = 1;
                if (cameraconfigParam.size() % sendmax == 0) {
                    block.nblockcount = cameraconfigParam.size() / sendmax;
                    eleSize = sendmax;
                } else {
                    block.nblockcount = cameraconfigParam.size() / sendmax + 1;
                    eleSize = sendmax;
                }
            }
            for (int i = 0; i < block.nblockcount; ++i) {
                // block.nblockcount = 1;
                block.nblockid = i + 1;
                char buff[1024 * 10] = {0};
                int temp = sendmax;
                if (cameraconfigParam.size() - (i * sendmax) < sendmax) {
                    temp = cameraconfigParam.size() - (i * sendmax);
                }
                memcpy(buff, &block, sizeof(block));
                memcpy(buff + sizeof(block), cameraconfigParam.data() + i * sendmax, temp);
                std::string strData = std::string(buff, sizeof(block) + temp);

                std::cout << "block.nblockid:" << block.nblockid << " temp:" << temp << std::endl;
                m_pITCPNetworkEngine->SendData(m_ClientInfo.dwSocketID, MDM_CS_ALG, SUB_CS_ALG_INFO,(void*) strData.data(),
                                               strData.size());
            }

            std::cout << " size: " << cameraconfigParam.size() << "-------------cameraconfigParam:" << cameraconfigParam
                      << std::endl;
        }
    }
}

// 时间事件
bool CAttemperEngineSink::OnEventTimer(uint32 dwTimerID) {
    switch (dwTimerID) {
    case IDI_CONNECT_CORRESPOND: {
        std::string strCorrespondAddress = sConfigMgr->Get("CorrespondNet", "BindIP", "127.0.0.1");
        uint16 wCorrespondPort = sConfigMgr->GetInt32("CorrespondNet", "Port", 8600);
        m_pITCPSocketService->Connect(strCorrespondAddress, wCorrespondPort);
        LOG_INFO("server.logon", "Connecting to the correspond server [ %s:%d ]", strCorrespondAddress,
                 wCorrespondPort);
        return true;
    }
    case IDI_CONNECT_CAMERA:
    {
        onCameraStatusChange();
        return true;
    }
    }
    return false;
}

bool CAttemperEngineSink::OnTCPSocketMainRegister(uint16 wSubCmdID, void *pData, uint16 wDataSize) {
    switch (wSubCmdID) {
    case SUB_CS_S_REGISTER_FAILURE: // 注册失败
    {
        // 变量定义
        CMD_CS_S_RegisterFailure *pRegisterFailure = (CMD_CS_S_RegisterFailure *)pData;

        // 效验参数
        assert(wDataSize >= (sizeof(CMD_CS_S_RegisterFailure) - sizeof(pRegisterFailure->szDescribeString)));
        if (wDataSize < (sizeof(CMD_CS_S_RegisterFailure) - sizeof(pRegisterFailure->szDescribeString)))
            return false;

        // 关闭处理
        // m_bNeekCorrespond = false;
        m_pITCPSocketService->CloseSocket();

        // 显示消息
        LOG_INFO("server.logon", "%s", pRegisterFailure->szDescribeString);

        // 事件通知
        ControlResult ControlResult;
        ControlResult.cbSuccess = 0;
        SrvUnitsMgr->PostControlRequest(UDC_CORRESPOND_RESULT, &ControlResult, sizeof(ControlResult));
        return true;
    }
    }

    return true;
}

bool CAttemperEngineSink::OnTCPSocketMainServiceInfo(uint16 wSubCmdID, void *pData, uint16 wDataSize) {
    switch (wSubCmdID) {
    case SUB_CS_S_ROOM_INFO: // 房间信息
    {
        m_RoomListManager.DisuseRoomItem();
        return true;
    }
    case SUB_CS_S_ROOM_ONLINE: // 房间人数
    {
        // 效验参数
        assert(wDataSize == sizeof(CMD_CS_S_RoomOnLine));
        if (wDataSize != sizeof(CMD_CS_S_RoomOnLine))
            return false;

        // 变量定义
        CMD_CS_S_RoomOnLine *pServerOnLine = (CMD_CS_S_RoomOnLine *)pData;

        tagGameRoom *pRoomItem = m_RoomListManager.SearchGameRoom(pServerOnLine->wServerID);
        if (pRoomItem == nullptr)
            return true;

        uint32 dwOldOnlineCount = 0, dwOldAndroidCount = 0;
        dwOldOnlineCount = pRoomItem->dwOnLineCount;
        dwOldAndroidCount = pRoomItem->dwAndroidCount;

        // 目录人数
        tagGameKind *pGameKindItem = m_RoomListManager.SearchGameKind(pRoomItem->wKindID);
        if (pGameKindItem != NULL) {
            // 在线人数
            pGameKindItem->dwOnLineCount -= dwOldOnlineCount;
            pGameKindItem->dwOnLineCount += pRoomItem->dwOnLineCount;

            // 机器人数
            pGameKindItem->dwAndroidCount -= dwOldAndroidCount;
            pGameKindItem->dwAndroidCount += pRoomItem->dwAndroidCount;
        }
        return true;
    }
    case SUB_CS_S_ROOM_REMOVE: // 房间删除
    {
        // 效验参数
        assert(wDataSize == sizeof(CMD_CS_S_RoomRemove));
        if (wDataSize != sizeof(CMD_CS_S_RoomRemove))
            return false;

        // 变量定义
        CMD_CS_S_RoomRemove *pRoomRemove = (CMD_CS_S_RoomRemove *)pData;

        m_RoomListManager.DeleteGameRoom(pRoomRemove->wServerID);
        return true;
    }
    case SUB_CS_S_ROOM_INSERT: // 房间插入
    {
        // 效验参数
        assert(wDataSize % sizeof(tagGameRoom) == 0);
        if (wDataSize % sizeof(tagGameRoom) != 0)
            return false;

        // 变量定义
        uint16 wItemCount = wDataSize / sizeof(tagGameRoom);
        tagGameRoom *pGameRoom = (tagGameRoom *)pData;

        // 更新数据
        for (uint16 i = 0; i < wItemCount; ++i) {
            m_RoomListManager.InsertGameRoom(pGameRoom++);
        }
        return true;
    }
    case SUB_CS_S_ROOM_FINISH: // 房间完成
    {
        // 事件处理
        ControlResult ControlResult;
        ControlResult.cbSuccess = 1;
        SrvUnitsMgr->PostControlRequest(UDC_CORRESPOND_RESULT, &ControlResult, sizeof(ControlResult));
        std::cout << "SUB_CS_S_ROOM_FINISH----------------------------" << std::endl;
        return true;
    }
    }
    return false;
}

bool CAttemperEngineSink::OnTCPNetworkMainMBLogon(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID) {
    switch (wSubCmdID) {
    case SUB_MB_LOGON_VISITOR: // 游客登录
    {
        return OnTCPNetworkSubMBLogonVisitor(pData, wDataSize, dwSocketID);
    }
    }
    return false;
}

bool CAttemperEngineSink::OnTCPNetworkMainCSImage(uint16 wSubCmdID, void *pData, uint16 wDataSize, uint32 dwSocketID) {
    switch (wSubCmdID) {

    case SUB_CS_SWARN_INFO: // 报警信息
    {
        return OnTCPNetworkSubCSImage(pData, wDataSize, dwSocketID);
    }
    }
    return false;
}

void print_stack_info() {
    pthread_attr_t attr;
    void *stack_addr;
    size_t stack_size;

    pthread_getattr_np(pthread_self(), &attr);
    pthread_attr_getstack(&attr, &stack_addr, &stack_size);

    // 计算已用栈空间
    volatile char dummy;
    size_t used = stack_size - (&dummy - (char *)stack_addr);

    std::cout << "栈总大小: " << stack_size << " 字节\n"
              << "已用栈空间: " << used << " 字节\n"
              << "剩余栈空间: " << (stack_size - used) << " 字节\n";

    pthread_attr_destroy(&attr);
}

bool CAttemperEngineSink::saveImageToFile(const std::vector<unsigned char> &imgbuffer, const std::string &filename) {
    std::ofstream outfile(filename, std::ios::binary);
    if (!outfile) {
        return false;
    }

    outfile.write(reinterpret_cast<const char *>(imgbuffer.data()), imgbuffer.size());
    return outfile.good();
}

bool CAttemperEngineSink::OnTCPNetworkSubCSImage(void *pData, uint16 wDataSize, uint32 dwSocketID) {
    TCP_block *p = (TCP_block *)pData;
    print_stack_info();
    std::cout << "=======OnTCPNetworkSubCSImage=========:" << this->data
              << "  currentSize:" << wDataSize - sizeof(TCP_block) << std::endl;
    {
        std::lock_guard<std::mutex> lock(m_mapMut);

        int realSize = wDataSize - sizeof(TCP_block);
        auto uuid = p->nblockGUID;
        std::cout << "======uuid======:" << uuid << std::endl;
        std::shared_ptr<MsgInfo> msgPtr = nullptr;
        if (p->nblockid == 1) {
            msgPtr = std::make_shared<MsgInfo>();
            msgPtr->msg;
            memset(msgPtr->msg, 0, sizeof(msgPtr->msg));
            // memset(this->img, 0, sizeof(this->img));
            if (realSize > sizeof(msgPtr->msg)) {
                memcpy(msgPtr->msg, pData + sizeof(TCP_block), sizeof(msgPtr->msg));
            } else {
                memcpy(msgPtr->msg, pData + sizeof(TCP_block), realSize);
            }
            m_msgPool[uuid] = msgPtr;
        } else {
            if (m_msgPool.find(uuid) != m_msgPool.end()) {
                msgPtr = m_msgPool.find(uuid)->second;
                int leftSize = Data_Size - msgPtr->count;
                if (realSize > leftSize) {
                    memcpy(msgPtr->msg + msgPtr->count, pData + sizeof(TCP_block), leftSize);
                } else {
                    memcpy(msgPtr->msg + msgPtr->count, pData + sizeof(TCP_block), realSize);
                }
            }
        }
        if (msgPtr) {
            msgPtr->count += realSize;
        }

        // this->data += realSize;
        if (p->nblockcount == p->nblockid) {
            /* code */
            if (msgPtr) {
                std::string jdata(msgPtr->msg, sizeof(msgPtr->msg));
                sWarn warn;
                // LOG_INFO("server.logon", "-------------------jdata:[%s]", jdata.c_str());
                try {
                    xpack::json::decode(jdata, warn);
                    std::cout << "=======OnTCPNetworkSubCSImage======1111111===algid:" << warn.algid
                              << " cameraid : " << warn.cameraid << " level : " << warn.level
                              << " m_nTime : " << warn.m_nTime << std::endl;

                    // 写写文件
                    // XGlobal_Get(CDirManager, pDir, Dir);
                    // std::string dateStr = Util::getCurrentTimeDayString();
                    // std::string timeStr = Util::getCurrentTimeHmssString();

                    // std::string imgPath =
                    //     fmt::format("{}/{}/{}", pDir->getPath(CDirManager::AlarmPath), warn.cameraid, dateStr);

                    // CTools::checkPath(imgPath);

                    // std::string imgFile = fmt::format("{}/{}.jpg", imgPath, timeStr);

                    

                    // saveImageToFile(warn.imgbuffer, imgFile);
                    // std::string img_file = fmt::format("{}/{}/{}/{}.jpg", pDir->getSubPath(CDirManager::AlarmPath),
                    //                                    warn.cameraid, dateStr, timeStr);
                    std::cout << "========imgpath===========:" << warn.imgpath  << std::endl;
                    auto stmt = DB::LogonDatabasePool.GetPreparedStatement(AI_BOX_CAMERA_ALARM_LOG_INSERT);
                    // "insert into alarm_log_table(`camera_id`,`date`,`time`,"
                    //"`type`,`level`,`image`)  values(?,?,?,?,?,?)
                    int index = 0;
                    auto ses = secondsSinceMidnight() ;
                    stmt->SetInt32(index++, warn.cameraid);
                    stmt->SetInt64(index++, warn.m_nTime);
                    stmt->SetInt64(index++, ses);
                    stmt->SetInt64(index++, warn.algid);
                    stmt->SetInt64(index++, warn.level);
                    stmt->SetString(index++, warn.imgpath);
                    DB::LogonDatabasePool.Query(stmt);

                } catch (const std::exception &e) {
                    std::cerr << e.what() << '\n';
                }
                m_msgPool.erase(uuid);
            }

            // std::string jdata(this->img, sizeof(this->img));
            // // LOG_INFO("server.logon", "Host:[%s] Port:[%d] ThreadCount:[%d]", strBindIP.c_str(), iPort,
            // iThreadCount); LOG_INFO("server.logon", "-------------------jdata:[%s]", jdata.c_str()); std::cout <<
            // "=======OnTCPNetworkSubCSImage======end===" << jdata << " totalSize:" << this->data
            //           << std::endl;
            // sWarn warn;
            // try {
            //     xpack::json::decode(jdata, warn);
            //     std::cout << "=======OnTCPNetworkSubCSImage======1111111===algid:" << warn.algid
            //               << " cameraid : " << warn.cameraid << " level : " << warn.level
            //               << " m_nTime : " << warn.m_nTime << std::endl;
            // } catch (const std::exception &e) {
            //     std::cerr << e.what() << '\n';
            // }

            // this->data = 0;
            // memset(this->img, 0, sizeof(this->img));
        }
    }

    return true;
}

bool CAttemperEngineSink::OnTCPNetworkSubMBLogonVisitor(void *pData, uint16 wDataSize, uint32 dwSocketID) {

    std::cout << "OnTCPNetworkSubMBLogonVisitor" << std::endl;

    uint8 sendData[1024] = {0};
    // TCP_Head *pHead = (TCP_Head *)sendData;
    // pHead->CommandInfo.wMainCmdID = MDM_MB_LOGON;        // 主协议ID
    // pHead->CommandInfo.wSubCmdID = SUB_MB_LOGON_VISITOR; // 子协议ID
    // pHead->TCPInfo.cbCheckCode = 0;
    // pHead->TCPInfo.cbDataKind = DK_MAPPED;
    CameraInfolist cameraList;
    xpack::json::decode(std::string((char *)pData, wDataSize), cameraList);
    for (auto &cameraInfo : cameraList.CameraInfoList) {
        std::cout << "ID: " << cameraInfo.ID << ", RTSP: " << cameraInfo.RTSP << ", IP: " << cameraInfo.IP << std::endl;
    }

    std::cout << "CameraInfolist size: " << cameraList.CameraInfoList.size() << std::endl;

    CameraInfolist test;
    for (int i = 0; i < 32; i++) {
        CameraInfo kk;
        kk.ID = i;
        kk.RTSP = "rtsp://admin:twdz321.@*************:554/hevc/ch1/main/av_stream";
        kk.IP = "*************:554";
        test.CameraInfoList.push_back(kk);
    }

    std::string cameraconfig = xpack::json::encode(test);

    // 填充协议头（示例值）

    // 组包

    // uint16 dataSize = sizeof(pHead) + sizeof(CameraInfo);
    // pHead->TCPInfo.wPacketSize = dataSize;

    // 加密
    // EncryptBuffer(sendData, dataSize, 0);
    // send((char*)sendData, dataSize);
    std::cout << "send size: " << cameraconfig.size() << std::endl;
    m_pITCPNetworkEngine->SendData(dwSocketID, MDM_CS_CAMERA, SUB_CS_CAMERA_INFO,(void*) cameraconfig.data(),
                                   cameraconfig.size());
    //    sizeof(CMD_CS_CameraInfo));
    // m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_LOGON, SUB_MB_LOGON_SUCCESS, &info,
    //                                sizeof(CMD_CS_CameraInfo));

    // 效验参数
    //  assert(wDataSize >= sizeof(CMD_MB_LogonVisitor));
    //  if (wDataSize < sizeof(CMD_MB_LogonVisitor))return false;

    // //变量定义
    // tagBindParameter * pBindParameter = (m_pBindParameter + dwSocketID);

    // //处理消息
    // CMD_MB_LogonVisitor * pLogonVisitor = (CMD_MB_LogonVisitor *)pData;

    // //设置连接
    // pBindParameter->cbClientKind = LinkType::LT_MOBILE;

    // LogonErrorCode eLogonErrorCode = LEC_NONE;
    // uint8 * pClientAddr = (uint8 *)&pBindParameter->dwClientAddr;
    // std::string strClientIP = StringFormat("%d.%d.%d.%d", pClientAddr[3], pClientAddr[2], pClientAddr[1],
    // pClientAddr[0]);

    // ////////////////////////////////////
    // std::string strMachine = StringUtility::WStringToString((wchar_t*)pLogonVisitor->szMachineID);
    // PreparedStatement *stmt = LogonDatabasePool.GetPreparedStatement(LOGON_SEL_LIMIT_ADDRESS);
    // stmt->SetString(0, strClientIP);
    // stmt->SetString(1, strMachine);
    // PreparedQueryResult result = LogonDatabasePool.Query(stmt);

    // if (result)
    // {
    // 	Field* field = result->Fetch();
    // 	while (field[3].GetInt8() == OPEN_SWITCH)
    // 	{
    // 		if (field[2].GetUInt32() < time(0))
    // 		{
    // 			//更新禁止信息
    // 			stmt = LogonDatabasePool.GetPreparedStatement(LOGON_UPD_LIMIT_ADDRESS);
    // 			stmt->SetInt8(0, 0);
    // 			stmt->SetInt8(1, 0);
    // 			stmt->SetInt8(2, 0);
    // 			stmt->SetString(3, strClientIP);
    // 			stmt->SetString(4, strMachine);
    // 			LogonDatabasePool.DirectExecute(stmt);
    // 			break;
    // 		}

    // 		if (field[0].GetInt8() == OPEN_SWITCH)
    // 		{
    // 			eLogonErrorCode = LEC_LIMIT_IP;
    // 			break;
    // 		}

    // 		if (field[1].GetInt8() == OPEN_SWITCH)
    // 		{
    // 			eLogonErrorCode = LEC_LIMIT_MAC;
    // 			break;
    // 		}

    // 		LOG_ERROR("server.logon", "禁止登录逻辑出错 IP: %s  MAC: %s", strClientIP.c_str(),
    // strMachine.c_str()); 		break;
    // 	}
    // }

    // //是否禁止登陆
    // LOGON_FAILURE(dwSocketID, eLogonErrorCode)

    // //查询用户信息
    // stmt = LogonDatabasePool.GetPreparedStatement(LOGON_SEL_VISITOR_ACCOUNT);
    // stmt->SetString(0, strMachine);
    // result = LogonDatabasePool.Query(stmt);
    // if (!result)
    // {
    // 	int game_id = 0;
    // 	stmt = LogonDatabasePool.GetPreparedStatement(LOGON_SEL_GAME_ID);
    // 	PreparedQueryResult result_id = LogonDatabasePool.Query(stmt);
    // 	if (result_id)
    // 	{
    // 		Field* field = result_id->Fetch();
    // 		game_id = field[0].GetInt32();

    // 		//更新标识
    // 		stmt = LogonDatabasePool.GetPreparedStatement(LOGON_UPD_GAME_ID);
    // 		LogonDatabasePool.DirectExecute(stmt);
    // 	}
    // 	else
    // 	{
    // 		LOG_ERROR("server.logon", "分配游客ID出错 IP: %s  MAC: %s", strClientIP.c_str(),
    // strMachine.c_str());
    // 	}

    // 	//插入游客用户
    // 	std::string strVisitor = StringFormat("Visitor%d", game_id);
    // 	stmt = LogonDatabasePool.GetPreparedStatement(LOGON_INS_VISITOR_ACCOUNT);

    // 	std::string strUTF8Visitor;
    // 	Util::StringUtility::ConsoleToUtf8(strVisitor, strUTF8Visitor);
    // 	stmt->SetString(0, strUTF8Visitor);
    // 	stmt->SetString(1, strUTF8Visitor);
    // 	stmt->SetString(2, "");
    // 	stmt->SetString(3, "1");
    // 	stmt->SetInt8(4, 100/*pBindParameter->cbClientKind*/);
    // 	stmt->SetString(5, strClientIP);
    // 	stmt->SetString(6, strMachine);
    // 	LogonDatabasePool.DirectExecute(stmt);

    // 	//重新查询游客
    // 	stmt = LogonDatabasePool.GetPreparedStatement(LOGON_SEL_VISITOR_ACCOUNT);
    // 	stmt->SetString(0, strMachine);
    // 	result = LogonDatabasePool.Query(stmt);
    // 	if (!result)
    // 	{
    // 		LOG_ERROR("server.logon", "Insert ID IP: %s  MAC: %s", strClientIP.c_str(), strMachine.c_str());
    // 		return false;
    // 	}
    // }

    // //获取游戏信息
    // Field* field = result->Fetch();
    // int id = field[0].GetInt32();
    // std::string account = field[1].GetString();
    // std::string username = field[2].GetString();
    // std::string sha_pass_hash = field[3].GetString();
    // std::string face_url = field[4].GetString();
    // int limit = field[5].GetInt8();

    // //账号冻结状态
    // if ((limit & LEC_LIMIT_FREEZE) > 0)
    // {
    // 	eLogonErrorCode = LEC_LIMIT_FREEZE;
    // }
    // LOGON_FAILURE(dwSocketID, eLogonErrorCode)

    // //更新登陆信息
    // stmt = LogonDatabasePool.GetPreparedStatement(LOGON_UPD_VISITOR_ACCOUNT);
    // stmt->SetString(0, strClientIP);
    // stmt->SetString(1, strMachine);
    // LogonDatabasePool.DirectExecute(stmt);

    // //////////////////////////////////////////////////////////////////////////////////////////
    // CMD_MB_LogonSuccess LogonSuccess;
    // memset(&LogonSuccess, 0, sizeof(LogonSuccess));

    // LogonSuccess.dwUserID = id;
    // LogonSuccess.dwGameID = id;
    // LogonSuccess.lUserScore = field[6].GetUInt64();

    // std::string strAnsiAccount;
    // Util::StringUtility::Utf8ToConsole(account, strAnsiAccount);
    // std::wstring wstrAccount = Util::StringUtility::StringToWString(strAnsiAccount);
    // swprintf((wchar_t*)LogonSuccess.szAccounts, sizeof(LogonSuccess.szAccounts), L"%ls", wstrAccount.c_str());

    // std::string strAnsiUsername;
    // Util::StringUtility::Utf8ToConsole(account, strAnsiUsername);
    // std::wstring wstrUsername = Util::StringUtility::StringToWString(strAnsiUsername);
    // swprintf((wchar_t*)LogonSuccess.szNickName, sizeof(LogonSuccess.szNickName), L"%ls", wstrAccount.c_str());

    // std::string strAnsiSHAPass;
    // Util::StringUtility::Utf8ToConsole(sha_pass_hash, strAnsiSHAPass);
    // std::wstring wstrSHAPass = Util::StringUtility::StringToWString(sha_pass_hash);
    // swprintf((wchar_t*)LogonSuccess.szDynamicPass, sizeof(LogonSuccess.szDynamicPass), L"%ls",
    // wstrSHAPass.c_str());

    // m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_LOGON, SUB_MB_LOGON_SUCCESS, &LogonSuccess,
    // sizeof(LogonSuccess));

    // //SendKindListInfo(dwSocketID);
    // SendRoomListInfo(dwSocketID, INVALID_WORD);
    // m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_LIST_FINISH);
    return true;
}

void CAttemperEngineSink::SendKindListInfo(uint32 dwSocketID) {
    // 网络数据
    uint16 wSendSize = 0;
    uint8 cbDataBuffer[SOCKET_TCP_PACKET];

    // 遍历数据
    KindItemMap kim = m_RoomListManager.TraverseKindList();
    for (KIM_IT it = kim.begin(); it != kim.end(); ++it) {
        // 发送数据
        if ((wSendSize + sizeof(CMD_MB_GameKindItem)) > sizeof(cbDataBuffer)) {
            m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_KIND_LIST, cbDataBuffer, wSendSize);
            wSendSize = 0;
        }

        CMD_MB_GameKindItem GameKindItem = {};
        GameKindItem.wSortID = it->second->wSortID;
        GameKindItem.wKindID = it->second->wKindID;
        GameKindItem.wGameID = it->second->wGameID;

        GameKindItem.dwOnLineCount = it->second->dwOnLineCount;
        GameKindItem.dwAndroidCount = it->second->dwAndroidCount;
        GameKindItem.dwFullCount = it->second->dwFullCount;

        //////////////////////////////////////
        memcpy(cbDataBuffer + wSendSize, &GameKindItem, sizeof(GameKindItem));
        wSendSize += sizeof(GameKindItem);
    }

    // 发送剩余
    if (wSendSize > 0)
        m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_KIND_LIST, cbDataBuffer, wSendSize);
    return;
}

void CAttemperEngineSink::SendRoomListInfo(uint32 dwSocketID, uint16 wKindID) {
    // 网络数据
    uint16 wSendSize = 0;
    uint8 cbDataBuffer[SOCKET_TCP_PACKET];

    // 遍历数据
    RoomItemMap rim = m_RoomListManager.TraverseRoomList();
    for (RIM_IT it = rim.begin(); it != rim.end(); ++it) {
        // 发送数据
        if ((wSendSize + sizeof(CMD_MB_GameRoomItem)) > sizeof(cbDataBuffer)) {
            m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_ROOM_LIST, cbDataBuffer, wSendSize);
            wSendSize = 0;
        }

        CMD_MB_GameRoomItem GameRoomItem = {};
        GameRoomItem.wKindID = it->second->wKindID;
        GameRoomItem.wSortID = it->second->wSortID;
        GameRoomItem.wServerID = it->second->wServerID;
        GameRoomItem.wServerKind = it->second->wServerKind;
        GameRoomItem.wServerLevel = it->second->wServerLevel;
        GameRoomItem.wServerPort = it->second->wServerPort;
        GameRoomItem.wTableCount = (uint8)it->second->wTableCount;
        GameRoomItem.lCellScore = it->second->lCellScore;
        GameRoomItem.lEnterScore = it->second->lEnterScore;

        GameRoomItem.dwServerRule = it->second->dwServerRule;

        GameRoomItem.dwOnLineCount = it->second->dwOnLineCount;
        GameRoomItem.dwAndroidCount = it->second->dwAndroidCount;
        GameRoomItem.dwFullCount = it->second->dwFullCount;

        std::wstring wstrServerAddr = Util::StringUtility::StringToWString(it->second->szServerName);
        swprintf((wchar_t *)GameRoomItem.szServerAddr, sizeof(GameRoomItem.szServerAddr), L"%ls",
                 wstrServerAddr.c_str());

        std::wstring wstrServerName = Util::StringUtility::StringToWString(it->second->szServerAddr);
        swprintf((wchar_t *)GameRoomItem.szServerName, sizeof(GameRoomItem.szServerName), L"%ls",
                 wstrServerName.c_str());

        if (wKindID == INVALID_WORD || wKindID == it->second->wKindID) {
            memcpy(cbDataBuffer + wSendSize, &GameRoomItem, sizeof(GameRoomItem));
            wSendSize += sizeof(GameRoomItem);
        }
    }

    if (wSendSize > 0)
        m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_SERVER_LIST, SUB_MB_ROOM_LIST, cbDataBuffer, wSendSize);
}

bool CAttemperEngineSink::OnLogonFailure(uint32 dwSocketID, LogonErrorCode &lec) {
    if (lec == LEC_NONE) {
        return false;
    }

    CMD_MB_LogonFailure LogonFailure;
    memset(&LogonFailure, 0, sizeof(LogonFailure));

    LogonFailure.lResultCode = lec;
    std::wstring wstrLogonError = Util::StringUtility::StringToWString(LogonError[lec]);
    swprintf((wchar_t *)LogonFailure.szDescribe, sizeof(LogonFailure.szDescribe), L"%ls", wstrLogonError.c_str());

    return m_pITCPNetworkEngine->SendData(dwSocketID, MDM_MB_LOGON, SUB_MB_LOGON_FAILURE, &LogonFailure,
                                          sizeof(LogonFailure));
}
} // namespace AiBox