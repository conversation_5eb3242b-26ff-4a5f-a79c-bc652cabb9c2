/usr/bin/c++ -fPIC -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -shared -Wl,-soname,DataBase.so -o DataBase.so CMakeFiles/DataBase.dir/DBExports.cpp.o CMakeFiles/DataBase.dir/DBUpdater.cpp.o CMakeFiles/DataBase.dir/DBWorker.cpp.o CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o CMakeFiles/DataBase.dir/Field.cpp.o CMakeFiles/DataBase.dir/MySQLConnection.cpp.o CMakeFiles/DataBase.dir/PreparedStatement.cpp.o CMakeFiles/DataBase.dir/QueryCallback.cpp.o CMakeFiles/DataBase.dir/QueryResult.cpp.o CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o  -Wl,-rpath,/home/<USER>/AI_Box/apilib/log:/home/<USER>/AI_Box/apilib/util: ../log/Log.so /usr/lib/x86_64-linux-gnu/libmysqlclient.so ../util/Util.so ../dep/fmt/fmt.a -ldl -lpthread -lssl -lcrypto -luuid 
