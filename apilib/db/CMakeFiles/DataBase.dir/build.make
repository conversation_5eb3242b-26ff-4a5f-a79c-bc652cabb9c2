# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/AI_Box

# Include any dependencies generated for this target.
include apilib/db/CMakeFiles/DataBase.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include apilib/db/CMakeFiles/DataBase.dir/compiler_depend.make

# Include the progress variables for this target.
include apilib/db/CMakeFiles/DataBase.dir/progress.make

# Include the compile flags for this target's objects.
include apilib/db/CMakeFiles/DataBase.dir/flags.make

apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o: apilib/db/DBExports.cpp
apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o -MF CMakeFiles/DataBase.dir/DBExports.cpp.o.d -o CMakeFiles/DataBase.dir/DBExports.cpp.o -c /home/<USER>/AI_Box/apilib/db/DBExports.cpp

apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/DBExports.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/DBExports.cpp > CMakeFiles/DataBase.dir/DBExports.cpp.i

apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/DBExports.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/DBExports.cpp -o CMakeFiles/DataBase.dir/DBExports.cpp.s

apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o: apilib/db/DBUpdater.cpp
apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o -MF CMakeFiles/DataBase.dir/DBUpdater.cpp.o.d -o CMakeFiles/DataBase.dir/DBUpdater.cpp.o -c /home/<USER>/AI_Box/apilib/db/DBUpdater.cpp

apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/DBUpdater.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/DBUpdater.cpp > CMakeFiles/DataBase.dir/DBUpdater.cpp.i

apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/DBUpdater.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/DBUpdater.cpp -o CMakeFiles/DataBase.dir/DBUpdater.cpp.s

apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o: apilib/db/DBWorker.cpp
apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o -MF CMakeFiles/DataBase.dir/DBWorker.cpp.o.d -o CMakeFiles/DataBase.dir/DBWorker.cpp.o -c /home/<USER>/AI_Box/apilib/db/DBWorker.cpp

apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/DBWorker.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/DBWorker.cpp > CMakeFiles/DataBase.dir/DBWorker.cpp.i

apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/DBWorker.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/DBWorker.cpp -o CMakeFiles/DataBase.dir/DBWorker.cpp.s

apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o: apilib/db/DBWorkerPool.cpp
apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o -MF CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o.d -o CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o -c /home/<USER>/AI_Box/apilib/db/DBWorkerPool.cpp

apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/DBWorkerPool.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/DBWorkerPool.cpp > CMakeFiles/DataBase.dir/DBWorkerPool.cpp.i

apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/DBWorkerPool.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/DBWorkerPool.cpp -o CMakeFiles/DataBase.dir/DBWorkerPool.cpp.s

apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o: apilib/db/Field.cpp
apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o -MF CMakeFiles/DataBase.dir/Field.cpp.o.d -o CMakeFiles/DataBase.dir/Field.cpp.o -c /home/<USER>/AI_Box/apilib/db/Field.cpp

apilib/db/CMakeFiles/DataBase.dir/Field.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/Field.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/Field.cpp > CMakeFiles/DataBase.dir/Field.cpp.i

apilib/db/CMakeFiles/DataBase.dir/Field.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/Field.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/Field.cpp -o CMakeFiles/DataBase.dir/Field.cpp.s

apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o: apilib/db/MySQLConnection.cpp
apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o -MF CMakeFiles/DataBase.dir/MySQLConnection.cpp.o.d -o CMakeFiles/DataBase.dir/MySQLConnection.cpp.o -c /home/<USER>/AI_Box/apilib/db/MySQLConnection.cpp

apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/MySQLConnection.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/MySQLConnection.cpp > CMakeFiles/DataBase.dir/MySQLConnection.cpp.i

apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/MySQLConnection.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/MySQLConnection.cpp -o CMakeFiles/DataBase.dir/MySQLConnection.cpp.s

apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o: apilib/db/PreparedStatement.cpp
apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o -MF CMakeFiles/DataBase.dir/PreparedStatement.cpp.o.d -o CMakeFiles/DataBase.dir/PreparedStatement.cpp.o -c /home/<USER>/AI_Box/apilib/db/PreparedStatement.cpp

apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/PreparedStatement.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/PreparedStatement.cpp > CMakeFiles/DataBase.dir/PreparedStatement.cpp.i

apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/PreparedStatement.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/PreparedStatement.cpp -o CMakeFiles/DataBase.dir/PreparedStatement.cpp.s

apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o: apilib/db/QueryCallback.cpp
apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o -MF CMakeFiles/DataBase.dir/QueryCallback.cpp.o.d -o CMakeFiles/DataBase.dir/QueryCallback.cpp.o -c /home/<USER>/AI_Box/apilib/db/QueryCallback.cpp

apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/QueryCallback.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/QueryCallback.cpp > CMakeFiles/DataBase.dir/QueryCallback.cpp.i

apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/QueryCallback.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/QueryCallback.cpp -o CMakeFiles/DataBase.dir/QueryCallback.cpp.s

apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o: apilib/db/QueryResult.cpp
apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o -MF CMakeFiles/DataBase.dir/QueryResult.cpp.o.d -o CMakeFiles/DataBase.dir/QueryResult.cpp.o -c /home/<USER>/AI_Box/apilib/db/QueryResult.cpp

apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/QueryResult.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/QueryResult.cpp > CMakeFiles/DataBase.dir/QueryResult.cpp.i

apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/QueryResult.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/QueryResult.cpp -o CMakeFiles/DataBase.dir/QueryResult.cpp.s

apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o: apilib/db/Implementation/LogonDatabase.cpp
apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o -MF CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o.d -o CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o -c /home/<USER>/AI_Box/apilib/db/Implementation/LogonDatabase.cpp

apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/Implementation/LogonDatabase.cpp > CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.i

apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/Implementation/LogonDatabase.cpp -o CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.s

apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o: apilib/db/CMakeFiles/DataBase.dir/flags.make
apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o: apilib/db/Implementation/TreasureDatabase.cpp
apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o: apilib/db/CMakeFiles/DataBase.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o -MF CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o.d -o CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o -c /home/<USER>/AI_Box/apilib/db/Implementation/TreasureDatabase.cpp

apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.i"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/AI_Box/apilib/db/Implementation/TreasureDatabase.cpp > CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.i

apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.s"
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/AI_Box/apilib/db/Implementation/TreasureDatabase.cpp -o CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.s

# Object files for target DataBase
DataBase_OBJECTS = \
"CMakeFiles/DataBase.dir/DBExports.cpp.o" \
"CMakeFiles/DataBase.dir/DBUpdater.cpp.o" \
"CMakeFiles/DataBase.dir/DBWorker.cpp.o" \
"CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o" \
"CMakeFiles/DataBase.dir/Field.cpp.o" \
"CMakeFiles/DataBase.dir/MySQLConnection.cpp.o" \
"CMakeFiles/DataBase.dir/PreparedStatement.cpp.o" \
"CMakeFiles/DataBase.dir/QueryCallback.cpp.o" \
"CMakeFiles/DataBase.dir/QueryResult.cpp.o" \
"CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o" \
"CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o"

# External object files for target DataBase
DataBase_EXTERNAL_OBJECTS =

apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/build.make
apilib/db/DataBase.so: apilib/log/Log.so
apilib/db/DataBase.so: /usr/lib/x86_64-linux-gnu/libmysqlclient.so
apilib/db/DataBase.so: apilib/util/Util.so
apilib/db/DataBase.so: apilib/dep/fmt/fmt.a
apilib/db/DataBase.so: /usr/lib/x86_64-linux-gnu/libssl.so
apilib/db/DataBase.so: /usr/lib/x86_64-linux-gnu/libcrypto.so
apilib/db/DataBase.so: apilib/db/CMakeFiles/DataBase.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/AI_Box/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking CXX shared library DataBase.so"
	cd /home/<USER>/AI_Box/apilib/db && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/DataBase.dir/link.txt --verbose=$(VERBOSE)
	cd /home/<USER>/AI_Box/apilib/db && /usr/bin/cmake -E copy /home/<USER>/AI_Box/apilib/db/DataBase.so /home/<USER>/AI_Box/bin/

# Rule to build all files generated by this target.
apilib/db/CMakeFiles/DataBase.dir/build: apilib/db/DataBase.so
.PHONY : apilib/db/CMakeFiles/DataBase.dir/build

apilib/db/CMakeFiles/DataBase.dir/clean:
	cd /home/<USER>/AI_Box/apilib/db && $(CMAKE_COMMAND) -P CMakeFiles/DataBase.dir/cmake_clean.cmake
.PHONY : apilib/db/CMakeFiles/DataBase.dir/clean

apilib/db/CMakeFiles/DataBase.dir/depend:
	cd /home/<USER>/AI_Box && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/AI_Box /home/<USER>/AI_Box/apilib/db /home/<USER>/AI_Box /home/<USER>/AI_Box/apilib/db /home/<USER>/AI_Box/apilib/db/CMakeFiles/DataBase.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : apilib/db/CMakeFiles/DataBase.dir/depend

