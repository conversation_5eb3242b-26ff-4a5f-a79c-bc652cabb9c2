
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/AI_Box/apilib/db/DBExports.cpp" "apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/DBExports.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/DBUpdater.cpp" "apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/DBUpdater.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/DBWorker.cpp" "apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/DBWorker.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/DBWorkerPool.cpp" "apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/DBWorkerPool.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/Field.cpp" "apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/Field.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/Implementation/LogonDatabase.cpp" "apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/Implementation/LogonDatabase.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/Implementation/TreasureDatabase.cpp" "apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/Implementation/TreasureDatabase.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/MySQLConnection.cpp" "apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/MySQLConnection.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/PreparedStatement.cpp" "apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/PreparedStatement.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/QueryCallback.cpp" "apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/QueryCallback.cpp.o.d"
  "/home/<USER>/AI_Box/apilib/db/QueryResult.cpp" "apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o" "gcc" "apilib/db/CMakeFiles/DataBase.dir/QueryResult.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
