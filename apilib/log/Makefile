# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/AI_Box

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/AI_Box

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/AI_Box && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/AI_Box/CMakeFiles /home/<USER>/AI_Box/apilib/log//CMakeFiles/progress.marks
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/log/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/AI_Box/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/log/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/log/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/log/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
apilib/log/CMakeFiles/Log.dir/rule:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 apilib/log/CMakeFiles/Log.dir/rule
.PHONY : apilib/log/CMakeFiles/Log.dir/rule

# Convenience name for target.
Log: apilib/log/CMakeFiles/Log.dir/rule
.PHONY : Log

# fast build rule for target.
Log/fast:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/build
.PHONY : Log/fast

Log.o: Log.cpp.o
.PHONY : Log.o

# target to build an object file
Log.cpp.o:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/Log.cpp.o
.PHONY : Log.cpp.o

Log.i: Log.cpp.i
.PHONY : Log.i

# target to preprocess a source file
Log.cpp.i:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/Log.cpp.i
.PHONY : Log.cpp.i

Log.s: Log.cpp.s
.PHONY : Log.s

# target to generate assembly for a file
Log.cpp.s:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/Log.cpp.s
.PHONY : Log.cpp.s

LogConsole.o: LogConsole.cpp.o
.PHONY : LogConsole.o

# target to build an object file
LogConsole.cpp.o:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.o
.PHONY : LogConsole.cpp.o

LogConsole.i: LogConsole.cpp.i
.PHONY : LogConsole.i

# target to preprocess a source file
LogConsole.cpp.i:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.i
.PHONY : LogConsole.cpp.i

LogConsole.s: LogConsole.cpp.s
.PHONY : LogConsole.s

# target to generate assembly for a file
LogConsole.cpp.s:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogConsole.cpp.s
.PHONY : LogConsole.cpp.s

LogFile.o: LogFile.cpp.o
.PHONY : LogFile.o

# target to build an object file
LogFile.cpp.o:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogFile.cpp.o
.PHONY : LogFile.cpp.o

LogFile.i: LogFile.cpp.i
.PHONY : LogFile.i

# target to preprocess a source file
LogFile.cpp.i:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogFile.cpp.i
.PHONY : LogFile.cpp.i

LogFile.s: LogFile.cpp.s
.PHONY : LogFile.s

# target to generate assembly for a file
LogFile.cpp.s:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogFile.cpp.s
.PHONY : LogFile.cpp.s

LogMessage.o: LogMessage.cpp.o
.PHONY : LogMessage.o

# target to build an object file
LogMessage.cpp.o:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.o
.PHONY : LogMessage.cpp.o

LogMessage.i: LogMessage.cpp.i
.PHONY : LogMessage.i

# target to preprocess a source file
LogMessage.cpp.i:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.i
.PHONY : LogMessage.cpp.i

LogMessage.s: LogMessage.cpp.s
.PHONY : LogMessage.s

# target to generate assembly for a file
LogMessage.cpp.s:
	cd /home/<USER>/AI_Box && $(MAKE) $(MAKESILENT) -f apilib/log/CMakeFiles/Log.dir/build.make apilib/log/CMakeFiles/Log.dir/LogMessage.cpp.s
.PHONY : LogMessage.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... Log"
	@echo "... Log.o"
	@echo "... Log.i"
	@echo "... Log.s"
	@echo "... LogConsole.o"
	@echo "... LogConsole.i"
	@echo "... LogConsole.s"
	@echo "... LogFile.o"
	@echo "... LogFile.i"
	@echo "... LogFile.s"
	@echo "... LogMessage.o"
	@echo "... LogMessage.i"
	@echo "... LogMessage.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/AI_Box && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

