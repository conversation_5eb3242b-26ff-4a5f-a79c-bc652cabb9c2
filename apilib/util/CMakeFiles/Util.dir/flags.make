# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# compile CX<PERSON> with /usr/bin/c++
CXX_DEFINES = -DASIO_STANDALONE -DBOOST_ASIO_STANDALONE -DFMT_USE_DELETED_FUNCTIONS -DFMT_USE_EXTERN_TEMPLATES -DFMT_USE_OVERRIDE -DFMT_USE_RVALUE_REFERENCES -DFMT_USE_VARIADIC_TEMPLATES -DLENDY_API_EXPORT_COMMON -DLENDY_API_USE_DYNAMIC_LIBS -DPROTOCOL_STREAM -DUtil_EXPORTS

CXX_INCLUDES = -I/home/<USER>/AI_Box/apilib -I/home/<USER>/AI_Box/apilib/define -I/home/<USER>/AI_Box/apilib/define/netdef -I/home/<USER>/AI_Box/apilib/dep/asio -I/home/<USER>/AI_Box/apilib/dep/asio/asio -I/home/<USER>/AI_Box/apilib/dep/asio/asio/detail -I/home/<USER>/AI_Box/apilib/dep/asio/asio/detail/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/experimental -I/home/<USER>/AI_Box/apilib/dep/asio/asio/experimental/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/generic -I/home/<USER>/AI_Box/apilib/dep/asio/asio/generic/detail -I/home/<USER>/AI_Box/apilib/dep/asio/asio/generic/detail/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ip -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ip/detail -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ip/detail/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ip/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/local -I/home/<USER>/AI_Box/apilib/dep/asio/asio/local/detail -I/home/<USER>/AI_Box/apilib/dep/asio/asio/local/detail/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/posix -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ssl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ssl/detail -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ssl/detail/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ssl/impl -I/home/<USER>/AI_Box/apilib/dep/asio/asio/ts -I/home/<USER>/AI_Box/apilib/dep/asio/asio/windows -I/home/<USER>/AI_Box/apilib/net -I/home/<USER>/AI_Box/apilib/db -I/home/<USER>/AI_Box/apilib/db/Implementation -I/home/<USER>/AI_Box/apilib/util -I/home/<USER>/AI_Box/apilib/util/CMakeFiles -I/home/<USER>/AI_Box/vision_data.h -I/home/<USER>/AI_Box/apilib/dep/fmt -I/usr/local/openssl-1.0.2/include -I/home/<USER>/AI_Box/apilib/dep/utf8cpp

CXX_FLAGS = -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -std=gnu++14 -fPIC

