/usr/bin/c++ -fPIC -std=c++14 -O3 -fPIC -O2 -g -DNDEBUG -rdynamic -shared -Wl,-soname,Net.so -o Net.so CMakeFiles/Net.dir/AsynchronismEngine.cpp.o CMakeFiles/Net.dir/AttemperEngine.cpp.o CMakeFiles/Net.dir/TCPNetworkEngine.cpp.o CMakeFiles/Net.dir/TCPNetworkItem.cpp.o CMakeFiles/Net.dir/TCPSocketService.cpp.o CMakeFiles/Net.dir/TimerEngine.cpp.o  -Wl,-rpath,/home/<USER>/AI_Box/apilib/log:/home/<USER>/AI_Box/apilib/util: ../log/Log.so ../util/Util.so ../dep/fmt/fmt.a -ldl -lpthread -lssl -lcrypto -luuid 
